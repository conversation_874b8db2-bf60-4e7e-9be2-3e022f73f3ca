import { 
  DocumentData, 
  DocumentReference, 
  DocumentSnapshot, 
  QueryDocumentSnapshot, 
  QuerySnapshot, 
  Transaction,
  WriteBatch,
  Timestamp,
  FieldValue,
  Query,
  WhereFilterOp
} from "firebase-admin/firestore";
import { firestore } from "../config/database";
import { logger } from "../config/logger";
import { 
  FirestoreDocument, 
  COLLECTIONS, 
  PaginationResult, 
  BaseQuery 
} from "../types/firestore";

export class FirestoreService {
  private db = firestore;

  // Generic CRUD operations
  async create<T extends FirestoreDocument>(
    collection: string, 
    data: Omit<T, "id" | "createdAt" | "updatedAt">,
    customId?: string
  ): Promise<T> {
    try {
      const now = Timestamp.now();
      const docData = {
        ...data,
        createdAt: now,
        updatedAt: now,
      };

      let docRef: DocumentReference;
      
      if (customId) {
        docRef = this.db.collection(collection).doc(customId);
        await docRef.set(docData);
      } else {
        docRef = await this.db.collection(collection).add(docData);
      }

      const doc = await docRef.get();
      return { id: doc.id, ...doc.data() } as T;
    } catch (error) {
      logger.error(`Error creating document in ${collection}:`, error);
      throw error;
    }
  }

  async findById<T extends FirestoreDocument>(
    collection: string, 
    id: string
  ): Promise<T | null> {
    try {
      const doc = await this.db.collection(collection).doc(id).get();
      
      if (!doc.exists) {
        return null;
      }

      return { id: doc.id, ...doc.data() } as T;
    } catch (error) {
      logger.error(`Error finding document by ID in ${collection}:`, error);
      throw error;
    }
  }

  async update<T extends FirestoreDocument>(
    collection: string, 
    id: string, 
    data: Partial<Omit<T, "id" | "createdAt">>
  ): Promise<T> {
    try {
      const docRef = this.db.collection(collection).doc(id);
      const updateData = {
        ...data,
        updatedAt: Timestamp.now(),
      };

      await docRef.update(updateData);
      const doc = await docRef.get();
      
      if (!doc.exists) {
        throw new Error(`Document with ID ${id} not found`);
      }

      return { id: doc.id, ...doc.data() } as T;
    } catch (error) {
      logger.error(`Error updating document in ${collection}:`, error);
      throw error;
    }
  }

  async delete(collection: string, id: string): Promise<void> {
    try {
      await this.db.collection(collection).doc(id).delete();
    } catch (error) {
      logger.error(`Error deleting document in ${collection}:`, error);
      throw error;
    }
  }

  async findMany<T extends FirestoreDocument>(
    collection: string,
    options: {
      where?: Array<{ field: string; operator: WhereFilterOp; value: any }>;
      orderBy?: { field: string; direction: "asc" | "desc" };
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<T[]> {
    try {
      let query: Query = this.db.collection(collection);

      // Apply where conditions
      if (options.where) {
        options.where.forEach(condition => {
          query = query.where(condition.field, condition.operator, condition.value);
        });
      }

      // Apply ordering
      if (options.orderBy) {
        query = query.orderBy(options.orderBy.field, options.orderBy.direction);
      }

      // Apply offset
      if (options.offset) {
        query = query.offset(options.offset);
      }

      // Apply limit
      if (options.limit) {
        query = query.limit(options.limit);
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as T));
    } catch (error) {
      logger.error(`Error finding documents in ${collection}:`, error);
      throw error;
    }
  }

  async findManyWithPagination<T extends FirestoreDocument>(
    collection: string,
    query: BaseQuery & {
      where?: Array<{ field: string; operator: WhereFilterOp; value: any }>;
      orderBy?: { field: string; direction: "asc" | "desc" };
    } = {}
  ): Promise<PaginationResult<T>> {
    try {
      const page = query.page || 1;
      const limit = query.limit || 10;
      const offset = (page - 1) * limit;

      // Get total count
      let countQuery: Query = this.db.collection(collection);
      if (query.where) {
        query.where.forEach(condition => {
          countQuery = countQuery.where(condition.field, condition.operator, condition.value);
        });
      }
      const countSnapshot = await countQuery.get();
      const total = countSnapshot.size;

      // Get paginated data
      const data = await this.findMany<T>(collection, {
        where: query.where,
        orderBy: query.orderBy || { field: "createdAt", direction: "desc" },
        limit,
        offset,
      });

      const pages = Math.ceil(total / limit);

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          pages,
          hasNext: page < pages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error(`Error finding documents with pagination in ${collection}:`, error);
      throw error;
    }
  }

  async count(
    collection: string,
    where?: Array<{ field: string; operator: WhereFilterOp; value: any }>
  ): Promise<number> {
    try {
      let query: Query = this.db.collection(collection);

      if (where) {
        where.forEach(condition => {
          query = query.where(condition.field, condition.operator, condition.value);
        });
      }

      const snapshot = await query.get();
      return snapshot.size;
    } catch (error) {
      logger.error(`Error counting documents in ${collection}:`, error);
      throw error;
    }
  }

  // Transaction support
  async runTransaction<T>(updateFunction: (transaction: Transaction) => Promise<T>): Promise<T> {
    try {
      return await this.db.runTransaction(updateFunction);
    } catch (error) {
      logger.error("Error running transaction:", error);
      throw error;
    }
  }

  // Batch operations
  createBatch(): WriteBatch {
    return this.db.batch();
  }

  async commitBatch(batch: WriteBatch): Promise<void> {
    try {
      await batch.commit();
    } catch (error) {
      logger.error("Error committing batch:", error);
      throw error;
    }
  }

  // Search functionality
  async search<T extends FirestoreDocument>(
    collection: string,
    searchField: string,
    searchTerm: string,
    options: {
      limit?: number;
      additionalWhere?: Array<{ field: string; operator: WhereFilterOp; value: any }>;
    } = {}
  ): Promise<T[]> {
    try {
      // Firestore doesn't support full-text search natively
      // This is a simple prefix search implementation
      const searchTermLower = searchTerm.toLowerCase();
      const searchTermEnd = searchTermLower + '\uf8ff';

      let query: Query = this.db.collection(collection)
        .where(searchField, ">=", searchTermLower)
        .where(searchField, "<=", searchTermEnd);

      // Apply additional where conditions
      if (options.additionalWhere) {
        options.additionalWhere.forEach(condition => {
          query = query.where(condition.field, condition.operator, condition.value);
        });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as T));
    } catch (error) {
      logger.error(`Error searching in ${collection}:`, error);
      throw error;
    }
  }

  // Utility methods
  generateId(): string {
    return this.db.collection("temp").doc().id;
  }

  timestamp(): Timestamp {
    return Timestamp.now();
  }

  fieldValue() {
    return FieldValue;
  }
}

// Export singleton instance
export const firestoreService = new FirestoreService();
