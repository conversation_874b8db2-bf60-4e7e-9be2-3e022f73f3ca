{"name": "pos-backend", "version": "1.0.0", "description": "Point of Sale Backend for F&B and Confectionery", "main": "dist/app.js", "scripts": {"dev": "nodemon -e ts,tsx --exec tsx src/app.ts", "build": "tsc", "start": "node dist/app.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "npx tsx prisma/seed.ts", "test": "echo \"Error: no test specified\" && exit 1", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "frontend:install": "cd frontend && npm install", "dev:all": "concurrently \"npm run dev\" \"npm run frontend:dev\""}, "keywords": ["pos", "point-of-sale", "f&b", "confectionery", "typescript"], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express-rate-limit": "^5.1.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.29", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.8.2", "@types/express": "^4.17.22", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "firebase": "^11.10.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "prisma": "^6.8.2", "winston": "^3.17.0", "zod": "^3.25.49"}}