import { OrderStatus, PaymentStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateOrderInput,
  CreatePaymentInput,
  OrderQuery,
  UpdateOrderInput,
  UpdatePaymentInput,
} from "../schemas/order";

export class OrderService {
  static async createOrder(data: CreateOrderInput, userId: string) {
    try {
      return await prisma.$transaction(async (tx) => {
        // Validate customer if provided
        if (data.customerId) {
          const customer = await tx.customer.findUnique({
            where: { id: data.customerId },
          });

          if (!customer || !customer.isActive) {
            throw new CustomError("Customer not found or inactive", 404);
          }
        }

        // Validate products and calculate totals
        let subtotal = 0;
        const orderItems = [];

        for (const item of data.items) {
          const product = await tx.product.findUnique({
            where: { id: item.productId },
          });

          if (!product || !product.isActive) {
            throw new CustomError(
              `Product not found or inactive: ${item.productId}`,
              404
            );
          }

          // Check inventory if tracking is enabled
          if (product.trackInventory && product.stockQuantity < item.quantity) {
            throw new CustomError(
              `Insufficient stock for product: ${product.name}. Available: ${product.stockQuantity}, Requested: ${item.quantity}`,
              400
            );
          }

          const itemTotal = product.price * item.quantity;
          subtotal += itemTotal;

          orderItems.push({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: product.price,
            totalPrice: itemTotal,
            notes: item.notes,
          });

          // Update inventory if tracking is enabled
          if (product.trackInventory) {
            await tx.product.update({
              where: { id: item.productId },
              data: {
                stockQuantity: {
                  decrement: item.quantity,
                },
              },
            });
          }
        }

        // Calculate total amount
        const totalAmount = subtotal + data.taxAmount - data.discountAmount;

        if (totalAmount < 0) {
          throw new CustomError("Total amount cannot be negative", 400);
        }

        // Generate order number
        const orderCount = await tx.order.count();
        const orderNumber = `ORD-${Date.now()}-${String(
          orderCount + 1
        ).padStart(4, "0")}`;

        // Create order
        const order = await tx.order.create({
          data: {
            orderNumber,
            subtotal,
            taxAmount: data.taxAmount,
            discountAmount: data.discountAmount,
            totalAmount,
            customerId: data.customerId,
            userId,
            orderItems: {
              create: orderItems,
            },
          },
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                phone: true,
              },
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                username: true,
              },
            },
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    sku: true,
                    type: true,
                  },
                },
              },
            },
            payments: true,
          },
        });

        logger.info(
          `Order created: ${order.orderNumber} (ID: ${order.id}) by user ${userId}`
        );
        return order;
      });
    } catch (error) {
      logger.error("Order creation failed:", error);
      throw error;
    }
  }

  static async getOrders(query: OrderQuery) {
    try {
      const {
        page,
        limit,
        search,
        status,
        customerId,
        userId,
        startDate,
        endDate,
        minAmount,
        maxAmount,
      } = query;
      const skip = (page - 1) * limit;

      const where: any = {};

      // Search functionality
      if (search) {
        where.OR = [
          { orderNumber: { contains: search, mode: "insensitive" } },
          {
            customer: { firstName: { contains: search, mode: "insensitive" } },
          },
          { customer: { lastName: { contains: search, mode: "insensitive" } } },
          { customer: { email: { contains: search, mode: "insensitive" } } },
        ];
      }

      // Filter by status
      if (status) {
        where.status = status;
      }

      // Filter by customer
      if (customerId) {
        where.customerId = customerId;
      }

      // Filter by user
      if (userId) {
        where.userId = userId;
      }

      // Date range filter
      if (startDate || endDate) {
        where.orderDate = {};
        if (startDate) where.orderDate.gte = new Date(startDate);
        if (endDate) where.orderDate.lte = new Date(endDate);
      }

      // Amount range filter
      if (minAmount !== undefined || maxAmount !== undefined) {
        where.totalAmount = {};
        if (minAmount !== undefined) where.totalAmount.gte = minAmount;
        if (maxAmount !== undefined) where.totalAmount.lte = maxAmount;
      }

      const [orders, total] = await Promise.all([
        prisma.order.findMany({
          where,
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                phone: true,
              },
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                username: true,
              },
            },
            _count: {
              select: {
                orderItems: true,
                payments: true,
              },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        prisma.order.count({ where }),
      ]);

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch orders:", error);
      throw error;
    }
  }

  static async getOrderById(id: string) {
    try {
      const order = await prisma.order.findUnique({
        where: { id },
        include: {
          customer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
          orderItems: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  type: true,
                  description: true,
                },
              },
            },
          },
          payments: {
            orderBy: { createdAt: "desc" },
          },
        },
      });

      if (!order) {
        throw new CustomError("Order not found", 404);
      }

      return order;
    } catch (error) {
      logger.error("Failed to fetch order:", error);
      throw error;
    }
  }

  static async updateOrder(id: string, data: UpdateOrderInput) {
    try {
      const existingOrder = await prisma.order.findUnique({
        where: { id },
        include: { orderItems: true },
      });

      if (!existingOrder) {
        throw new CustomError("Order not found", 404);
      }

      // Validate status transitions
      if (data.status) {
        const validTransitions = this.getValidStatusTransitions(
          existingOrder.status
        );
        if (!validTransitions.includes(data.status)) {
          throw new CustomError(
            `Invalid status transition from ${existingOrder.status} to ${data.status}`,
            400
          );
        }
      }

      // Validate customer if being updated
      if (data.customerId) {
        const customer = await prisma.customer.findUnique({
          where: { id: data.customerId },
        });

        if (!customer || !customer.isActive) {
          throw new CustomError("Customer not found or inactive", 404);
        }
      }

      // Recalculate total if tax or discount changed
      let updateData: any = { ...data };
      if (data.taxAmount !== undefined || data.discountAmount !== undefined) {
        const taxAmount = data.taxAmount ?? existingOrder.taxAmount;
        const discountAmount =
          data.discountAmount ?? existingOrder.discountAmount;
        const totalAmount = existingOrder.subtotal + taxAmount - discountAmount;

        if (totalAmount < 0) {
          throw new CustomError("Total amount cannot be negative", 400);
        }

        updateData.totalAmount = totalAmount;
      }

      const order = await prisma.order.update({
        where: { id },
        data: updateData,
        include: {
          customer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            },
          },
          orderItems: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  type: true,
                },
              },
            },
          },
          payments: true,
        },
      });

      logger.info(`Order updated: ${order.orderNumber} (ID: ${order.id})`);
      return order;
    } catch (error) {
      logger.error("Order update failed:", error);
      throw error;
    }
  }

  static async createPayment(orderId: string, data: CreatePaymentInput) {
    try {
      return await prisma.$transaction(async (tx) => {
        const order = await tx.order.findUnique({
          where: { id: orderId },
          include: { payments: true },
        });

        if (!order) {
          throw new CustomError("Order not found", 404);
        }

        // Calculate total paid amount
        const totalPaid = order.payments
          .filter((p) => p.status === PaymentStatus.COMPLETED)
          .reduce((sum, p) => sum + p.amount, 0);

        const remainingAmount = order.totalAmount - totalPaid;

        if (data.amount > remainingAmount) {
          throw new CustomError(
            `Payment amount (${data.amount}) exceeds remaining balance (${remainingAmount})`,
            400
          );
        }

        const payment = await tx.payment.create({
          data: {
            ...data,
            orderId,
            status: PaymentStatus.COMPLETED, // Auto-complete for now
          },
        });

        // Check if order is fully paid
        const newTotalPaid = totalPaid + data.amount;
        if (
          newTotalPaid >= order.totalAmount &&
          order.status === OrderStatus.PENDING
        ) {
          await tx.order.update({
            where: { id: orderId },
            data: { status: OrderStatus.PREPARING },
          });
        }

        logger.info(
          `Payment created for order ${order.orderNumber}: ${data.amount} via ${data.method}`
        );
        return payment;
      });
    } catch (error) {
      logger.error("Payment creation failed:", error);
      throw error;
    }
  }

  static async updatePayment(
    orderId: string,
    paymentId: string,
    data: UpdatePaymentInput
  ) {
    try {
      const payment = await prisma.payment.findFirst({
        where: {
          id: paymentId,
          orderId,
        },
      });

      if (!payment) {
        throw new CustomError("Payment not found", 404);
      }

      const updatedPayment = await prisma.payment.update({
        where: { id: paymentId },
        data,
      });

      logger.info(`Payment updated: ${paymentId} for order ${orderId}`);
      return updatedPayment;
    } catch (error) {
      logger.error("Payment update failed:", error);
      throw error;
    }
  }

  private static getValidStatusTransitions(
    currentStatus: OrderStatus
  ): OrderStatus[] {
    const transitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.PREPARING, OrderStatus.CANCELLED],
      [OrderStatus.PREPARING]: [OrderStatus.READY, OrderStatus.CANCELLED],
      [OrderStatus.READY]: [OrderStatus.COMPLETED, OrderStatus.CANCELLED],
      [OrderStatus.COMPLETED]: [], // No transitions from completed
      [OrderStatus.CANCELLED]: [], // No transitions from cancelled
    };

    return transitions[currentStatus] || [];
  }
}
