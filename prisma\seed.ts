import { PrismaClient, UserRole, ProductType } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: adminPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: UserRole.ADMIN,
      isActive: true
    }
  });
  console.log('✅ Admin user created:', admin.email);

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 12);
  const manager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'manager',
      password: managerPassword,
      firstName: 'Store',
      lastName: 'Manager',
      role: UserRole.MANAGER,
      isActive: true
    }
  });
  console.log('✅ Manager user created:', manager.email);

  // Create cashier user
  const cashierPassword = await bcrypt.hash('cashier123', 12);
  const cashier = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'cashier',
      password: cashierPassword,
      firstName: 'Store',
      lastName: 'Cashier',
      role: UserRole.CASHIER,
      isActive: true
    }
  });
  console.log('✅ Cashier user created:', cashier.email);

  // Create categories
  const beverageCategory = await prisma.category.upsert({
    where: { name: 'Beverages' },
    update: {},
    create: {
      name: 'Beverages',
      description: 'Hot and cold drinks',
      isActive: true
    }
  });

  const foodCategory = await prisma.category.upsert({
    where: { name: 'Food' },
    update: {},
    create: {
      name: 'Food',
      description: 'Main dishes and snacks',
      isActive: true
    }
  });

  const confectioneryCategory = await prisma.category.upsert({
    where: { name: 'Confectionery' },
    update: {},
    create: {
      name: 'Confectionery',
      description: 'Sweets, cakes, and desserts',
      isActive: true
    }
  });

  console.log('✅ Categories created');

  // Create sample products
  const products = [
    {
      name: 'Espresso',
      description: 'Strong black coffee',
      price: 3.50,
      cost: 1.20,
      sku: 'BEV001',
      type: ProductType.BEVERAGE,
      categoryId: beverageCategory.id,
      stockQuantity: 100,
      preparationTime: 2,
      calories: 5
    },
    {
      name: 'Cappuccino',
      description: 'Coffee with steamed milk and foam',
      price: 4.50,
      cost: 1.80,
      sku: 'BEV002',
      type: ProductType.BEVERAGE,
      categoryId: beverageCategory.id,
      stockQuantity: 100,
      preparationTime: 3,
      calories: 120
    },
    {
      name: 'Chocolate Croissant',
      description: 'Buttery pastry with chocolate filling',
      price: 3.25,
      cost: 1.50,
      sku: 'FOOD001',
      type: ProductType.FOOD,
      categoryId: foodCategory.id,
      stockQuantity: 50,
      preparationTime: 1,
      calories: 280
    },
    {
      name: 'Chocolate Cake Slice',
      description: 'Rich chocolate cake with ganache',
      price: 5.95,
      cost: 2.50,
      sku: 'CONF001',
      type: ProductType.CONFECTIONERY,
      categoryId: confectioneryCategory.id,
      stockQuantity: 20,
      preparationTime: 0,
      calories: 450
    }
  ];

  for (const product of products) {
    await prisma.product.upsert({
      where: { sku: product.sku },
      update: {},
      create: product
    });
  }

  console.log('✅ Sample products created');

  console.log('🎉 Database seeding completed!');
  console.log('\n📋 Default users created:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Manager: <EMAIL> / manager123');
  console.log('Cashier: <EMAIL> / cashier123');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
