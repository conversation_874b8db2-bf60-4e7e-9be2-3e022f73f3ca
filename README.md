# POS System - Point of Sale Management

Sistem Point of Sale (POS) yang komprehensif untuk bisnis F&B dan Confectionery dengan backend TypeScript dan frontend Next.js.

## 🚀 Features

### Backend Features
- **Authentication & Authorization**: JWT-based dengan role-based access control (ADMIN, MANAGER, CASHIER)
- **Product Management**: CRUD operations dengan kategori, stok, dan tipe produk
- **Order Management**: Sistem pemesanan lengkap dengan status tracking
- **Customer Management**: Database pelanggan dengan riwayat pembelian
- **Payment Processing**: Multiple payment methods (Cash, Card, Digital Wallet)
- **Analytics**: Dashboard dengan laporan penjualan dan analytics
- **Inventory Management**: Stock tracking dengan low stock alerts

### Frontend Features
- **Modern UI**: Next.js 15 dengan App Router dan Tailwind CSS
- **Responsive Design**: Mobile-first design yang responsive
- **Real-time Updates**: Live inventory dan order status updates
- **POS Interface**: User-friendly point of sale interface
- **Dashboard Analytics**: Comprehensive sales dashboard
- **Role-based Access**: Different interfaces berdasarkan user role

## 🛠 Tech Stack

### Backend
- **Runtime**: Node.js dengan TypeScript
- **Framework**: Express.js
- **Database**: SQLite dengan Prisma ORM
- **Authentication**: JWT dengan bcrypt
- **Validation**: Zod schema validation
- **Logging**: Winston untuk structured logging
- **API Documentation**: Comprehensive REST API

### Frontend
- **Framework**: Next.js 15 dengan App Router
- **Language**: TypeScript dengan strict typing
- **Styling**: Tailwind CSS dengan custom components
- **State Management**: Zustand untuk global state
- **Forms**: React Hook Form dengan Zod validation
- **HTTP Client**: Axios dengan interceptors
- **UI Components**: Headless UI dan Heroicons
- **Notifications**: React Hot Toast

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- npm atau yarn
- Git

### Backend Setup
```bash
# Clone repository
git clone <your-repo-url>
cd pos-backend

# Install dependencies
npm install

# Setup database
npx prisma generate
npx prisma db push

# Seed database (optional)
npm run seed

# Start development server
npm run dev
```

### Frontend Setup
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

## 🌐 Deployment

### Deploy ke Vercel

#### Frontend Deployment
1. **Push ke GitHub**:
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

2. **Deploy di Vercel**:
   - Kunjungi [vercel.com](https://vercel.com)
   - Connect dengan GitHub account
   - Import repository ini
   - Pilih `frontend` folder sebagai root directory
   - Set environment variables:
     ```
     NEXT_PUBLIC_API_URL=https://your-backend-url.vercel.app
     ```
   - Deploy!

#### Backend Deployment
1. **Buat project terpisah untuk backend**:
   - Import repository yang sama
   - Pilih root directory (untuk backend)
   - Set environment variables:
     ```
     DATABASE_URL=your-database-url
     JWT_SECRET=your-jwt-secret
     NODE_ENV=production
     ```

### Environment Variables

#### Backend (.env)
```env
# Database
DATABASE_URL="file:./dev.db"

# JWT
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"

# Server
PORT=3000
NODE_ENV="development"

# CORS
FRONTEND_URL="http://localhost:3001"
```

#### Frontend (.env.local)
```env
# API Configuration
NEXT_PUBLIC_API_URL="http://localhost:3000"
```

## 📱 Usage

### Default Admin Account
Setelah seeding database, gunakan akun berikut:
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: ADMIN

### API Endpoints

#### Authentication
- `POST /api/auth/register` - Register user baru
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user

#### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create product
- `GET /api/products/:id` - Get product by ID
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

#### Orders
- `GET /api/orders` - Get all orders
- `POST /api/orders` - Create new order
- `GET /api/orders/:id` - Get order by ID
- `PATCH /api/orders/:id/status` - Update order status
- `POST /api/orders/:id/payments` - Add payment to order

#### Customers
- `GET /api/customers` - Get all customers
- `POST /api/customers` - Create customer
- `GET /api/customers/:id` - Get customer by ID
- `PUT /api/customers/:id` - Update customer

## 🔧 Development

### Database Management
```bash
# Generate Prisma client
npx prisma generate

# Push schema changes
npx prisma db push

# View database
npx prisma studio

# Reset database
npx prisma db push --force-reset
```

### Code Quality
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Format code
npm run format
```

## 📊 Project Structure

```
pos-backend/
├── src/
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Express middleware
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── types/          # TypeScript types
│   ├── utils/          # Utility functions
│   └── server.ts       # Main server file
├── prisma/
│   ├── schema.prisma   # Database schema
│   └── seed.ts         # Database seeding
├── frontend/
│   ├── app/            # Next.js App Router
│   ├── components/     # React components
│   ├── lib/            # Utilities & services
│   ├── store/          # Zustand stores
│   └── types/          # TypeScript types
└── vercel.json         # Vercel deployment config
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

Jika ada pertanyaan atau masalah:
1. Check existing issues di GitHub
2. Create new issue dengan detail yang jelas
3. Atau contact developer

## 🚀 Live Demo

- **Frontend**: [https://your-frontend.vercel.app](https://your-frontend.vercel.app)
- **Backend API**: [https://your-backend.vercel.app](https://your-backend.vercel.app)

---

**Happy Coding! 🎉**
