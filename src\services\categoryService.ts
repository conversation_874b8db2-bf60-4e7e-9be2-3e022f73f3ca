import { prisma } from '../config/database';
import { CustomError } from '../middleware/errorHandler';
import { CreateCategoryInput, UpdateCategoryInput, CategoryQuery } from '../schemas/category';
import { logger } from '../config/logger';

export class CategoryService {
  static async createCategory(data: CreateCategoryInput) {
    try {
      // Check if category already exists
      const existingCategory = await prisma.category.findUnique({
        where: { name: data.name }
      });

      if (existingCategory) {
        throw new CustomError('Category with this name already exists', 409);
      }

      const category = await prisma.category.create({
        data,
        include: {
          _count: {
            select: { products: true }
          }
        }
      });

      logger.info(`Category created: ${category.name}`);
      return category;
    } catch (error) {
      logger.error('Category creation failed:', error);
      throw error;
    }
  }

  static async getCategories(query: CategoryQuery) {
    try {
      const { page, limit, search, isActive } = query;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const [categories, total] = await Promise.all([
        prisma.category.findMany({
          where,
          include: {
            _count: {
              select: { products: true }
            }
          },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.category.count({ where })
      ]);

      return {
        categories,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to fetch categories:', error);
      throw error;
    }
  }

  static async getCategoryById(id: string) {
    try {
      const category = await prisma.category.findUnique({
        where: { id },
        include: {
          _count: {
            select: { products: true }
          }
        }
      });

      if (!category) {
        throw new CustomError('Category not found', 404);
      }

      return category;
    } catch (error) {
      logger.error('Failed to fetch category:', error);
      throw error;
    }
  }

  static async updateCategory(id: string, data: UpdateCategoryInput) {
    try {
      const existingCategory = await prisma.category.findUnique({
        where: { id }
      });

      if (!existingCategory) {
        throw new CustomError('Category not found', 404);
      }

      // Check for name conflicts
      if (data.name) {
        const conflictCategory = await prisma.category.findFirst({
          where: {
            AND: [
              { id: { not: id } },
              { name: data.name }
            ]
          }
        });

        if (conflictCategory) {
          throw new CustomError('Category name already exists', 409);
        }
      }

      const category = await prisma.category.update({
        where: { id },
        data,
        include: {
          _count: {
            select: { products: true }
          }
        }
      });

      logger.info(`Category updated: ${category.name}`);
      return category;
    } catch (error) {
      logger.error('Category update failed:', error);
      throw error;
    }
  }

  static async deleteCategory(id: string) {
    try {
      const category = await prisma.category.findUnique({
        where: { id },
        include: {
          _count: {
            select: { products: true }
          }
        }
      });

      if (!category) {
        throw new CustomError('Category not found', 404);
      }

      if (category._count.products > 0) {
        throw new CustomError('Cannot delete category with existing products', 400);
      }

      await prisma.category.delete({
        where: { id }
      });

      logger.info(`Category deleted: ${category.name}`);
      return { message: 'Category deleted successfully' };
    } catch (error) {
      logger.error('Category deletion failed:', error);
      throw error;
    }
  }
}
