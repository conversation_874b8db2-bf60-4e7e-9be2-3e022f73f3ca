// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User roles for the POS system
enum UserRole {
  ADMIN
  MANAGER
  CASHIER
}

// Order status tracking
enum OrderStatus {
  PENDING
  PREPARING
  READY
  COMPLETED
  CANCELLED
}

// Payment methods
enum PaymentMethod {
  CASH
  CARD
  DIGITAL_WALLET
  BANK_TRANSFER
}

// Payment status
enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

// Product types for F&B and confectionery
enum ProductType {
  FOOD
  BEVERAGE
  CONFECTIONERY
  COMBO
}

// Users table - staff members who can access the system
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String
  lastName  String
  role      UserRole @default(CASHIER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  orders Order[]

  // Indexes for performance
  @@index([email])
  @@index([username])
  @@index([role])
  @@index([isActive])
  @@index([createdAt])
  @@map("users")
}

// Categories for organizing products
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products Product[]

  // Indexes for performance
  @@index([name])
  @@index([isActive])
  @@index([createdAt])
  @@map("categories")
}

// Products/Menu items
model Product {
  id          String      @id @default(cuid())
  name        String
  description String?
  price       Float
  cost        Float? // Cost price for profit calculation
  sku         String?     @unique
  barcode     String?     @unique
  type        ProductType
  isActive    Boolean     @default(true)

  // Inventory fields
  stockQuantity  Int     @default(0)
  minStockLevel  Int     @default(0)
  trackInventory Boolean @default(true)

  // F&B specific fields
  preparationTime Int? // in minutes
  calories        Int?
  allergens       String? // JSON string of allergens

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  categoryId String
  category   Category    @relation(fields: [categoryId], references: [id])
  orderItems OrderItem[]

  // Indexes for performance
  @@index([name])
  @@index([sku])
  @@index([barcode])
  @@index([type])
  @@index([isActive])
  @@index([categoryId])
  @@index([stockQuantity])
  @@index([createdAt])
  @@map("products")
}

// Customers (optional for loyalty programs)
model Customer {
  id          String    @id @default(cuid())
  firstName   String?
  lastName    String?
  email       String?   @unique
  phone       String?   @unique
  dateOfBirth DateTime?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  orders Order[]

  // Indexes for performance
  @@index([email])
  @@index([phone])
  @@index([isActive])
  @@index([createdAt])
  @@map("customers")
}

// Orders - main transaction entity
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  status      OrderStatus @default(PENDING)

  // Totals
  subtotal       Float
  taxAmount      Float @default(0)
  discountAmount Float @default(0)
  totalAmount    Float

  // Customer info (can be null for walk-in customers)
  customerId String?
  customer   Customer? @relation(fields: [customerId], references: [id])

  // Staff who created the order
  userId String
  user   User   @relation(fields: [userId], references: [id])

  // Timestamps
  orderDate   DateTime  @default(now())
  completedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  orderItems OrderItem[]
  payments   Payment[]

  // Indexes for performance
  @@index([orderNumber])
  @@index([status])
  @@index([customerId])
  @@index([userId])
  @@index([orderDate])
  @@index([createdAt])
  @@index([totalAmount])
  @@map("orders")
}

// Order items - products in an order
model OrderItem {
  id         String  @id @default(cuid())
  quantity   Int
  unitPrice  Float
  totalPrice Float
  notes      String? // Special instructions

  // Relations
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation(fields: [productId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([orderId])
  @@index([productId])
  @@index([createdAt])
  @@map("order_items")
}

// Payments for orders
model Payment {
  id        String        @id @default(cuid())
  amount    Float
  method    PaymentMethod
  status    PaymentStatus @default(PENDING)
  reference String? // Transaction reference from payment gateway
  notes     String?

  // Relations
  orderId String
  order   Order  @relation(fields: [orderId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([orderId])
  @@index([method])
  @@index([status])
  @@index([reference])
  @@index([createdAt])
  @@map("payments")
}
