import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { validate } from '../middleware/validation';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '@prisma/client';
import { 
  createUserSchema, 
  updateUserSchema, 
  userParamsSchema, 
  userQuerySchema 
} from '../schemas/user';

const router = Router();

// All user routes require authentication
router.use(authenticate);

// Get all users (ADMIN and MANAGER only)
router.get('/', 
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(userQuerySchema),
  UserController.getUsers
);

// Create user (ADMIN only)
router.post('/', 
  authorize(UserRole.ADMIN),
  validate(createUserSchema),
  UserController.createUser
);

// Get user by ID (ADMIN and MANAGER only)
router.get('/:id', 
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(userParamsSchema),
  UserController.getUserById
);

// Update user (ADMIN only)
router.put('/:id', 
  authorize(UserRole.ADMIN),
  validate(userParamsSchema),
  validate(updateUserSchema),
  UserController.updateUser
);

// Delete user (ADMIN only)
router.delete('/:id', 
  authorize(UserRole.ADMIN),
  validate(userParamsSchema),
  UserController.deleteUser
);

export default router;
