import { z } from 'zod';
import { UserRole } from '@prisma/client';

export const createUserSchema = z.object({
  body: z.object({
    email: z.string().email('Invalid email format'),
    username: z.string().min(3, 'Username must be at least 3 characters'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    role: z.nativeEnum(UserRole).default(UserRole.CASHIER)
  })
});

export const updateUserSchema = z.object({
  body: z.object({
    email: z.string().email('Invalid email format').optional(),
    username: z.string().min(3, 'Username must be at least 3 characters').optional(),
    firstName: z.string().min(1, 'First name is required').optional(),
    lastName: z.string().min(1, 'Last name is required').optional(),
    role: z.nativeEnum(UserRole).optional(),
    isActive: z.boolean().optional()
  })
});

export const userParamsSchema = z.object({
  params: z.object({
    id: z.string().cuid('Invalid user ID format')
  })
});

export const userQuerySchema = z.object({
  query: z.object({
    page: z.string().transform(Number).pipe(z.number().min(1)).optional().default('1'),
    limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional().default('10'),
    search: z.string().optional(),
    role: z.nativeEnum(UserRole).optional(),
    isActive: z.string().transform((val) => val === 'true').optional()
  })
});

export type CreateUserInput = z.infer<typeof createUserSchema>['body'];
export type UpdateUserInput = z.infer<typeof updateUserSchema>['body'];
export type UserParams = z.infer<typeof userParamsSchema>['params'];
export type UserQuery = z.infer<typeof userQuerySchema>['query'];
