import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import { UserRole } from "../types/firestore";
import { CustomError } from "./errorHandler";

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    username: string;
  };
}

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      throw new CustomError("Access token required", 401);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!process.env.JWT_SECRET) {
      throw new CustomError("JWT secret not configured", 500);
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;

    // Verify user still exists and is active
    // Import Firestore service and types
    const { firestoreService } = await import("../services/firestoreService");
    const { COLLECTIONS } = await import("../types/firestore");
    type User = import("../types/firestore").User;

    const user = await firestoreService.findById<User>(
      COLLECTIONS.USERS,
      decoded.userId
    );

    if (!user || !user.isActive) {
      throw new CustomError("Invalid or expired token", 401);
    }

    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new CustomError("Invalid token", 401));
    } else {
      next(error);
    }
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new CustomError("Authentication required", 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new CustomError("Insufficient permissions", 403));
    }

    next();
  };
};
