import { logger } from "../config/logger";
import { CustomError } from "../middleware/errorHandler";
import {
  CreateCustomerInput,
  CustomerQuery,
  UpdateCustomerInput,
} from "../schemas/customer";

export class CustomerService {
  static async createCustomer(data: CreateCustomerInput) {
    try {
      // Check for email uniqueness if provided
      if (data.email) {
        const existingCustomer = await prisma.customer.findUnique({
          where: { email: data.email },
        });

        if (existingCustomer) {
          throw new CustomError("Customer with this email already exists", 409);
        }
      }

      // Check for phone uniqueness if provided
      if (data.phone) {
        const existingCustomer = await prisma.customer.findUnique({
          where: { phone: data.phone },
        });

        if (existingCustomer) {
          throw new CustomError(
            "Customer with this phone number already exists",
            409
          );
        }
      }

      const customer = await prisma.customer.create({
        data,
        include: {
          _count: {
            select: { orders: true },
          },
        },
      });

      logger.info(
        `Customer created: ${customer.firstName} ${customer.lastName} (ID: ${customer.id})`
      );
      return customer;
    } catch (error) {
      logger.error("Customer creation failed:", error);
      throw error;
    }
  }

  static async getCustomers(query: CustomerQuery) {
    try {
      const { page, limit, search, isActive } = query;
      const skip = (page - 1) * limit;

      const where: any = {};

      // Search functionality
      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: "insensitive" } },
          { lastName: { contains: search, mode: "insensitive" } },
          { email: { contains: search, mode: "insensitive" } },
          { phone: { contains: search, mode: "insensitive" } },
        ];
      }

      // Filter by active status
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const [customers, total] = await Promise.all([
        prisma.customer.findMany({
          where,
          include: {
            _count: {
              select: { orders: true },
            },
          },
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
        }),
        prisma.customer.count({ where }),
      ]);

      return {
        customers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Failed to fetch customers:", error);
      throw error;
    }
  }

  static async getCustomerById(id: string) {
    try {
      const customer = await prisma.customer.findUnique({
        where: { id },
        include: {
          _count: {
            select: { orders: true },
          },
          orders: {
            take: 5,
            orderBy: { createdAt: "desc" },
            select: {
              id: true,
              orderNumber: true,
              status: true,
              totalAmount: true,
              orderDate: true,
            },
          },
        },
      });

      if (!customer) {
        throw new CustomError("Customer not found", 404);
      }

      return customer;
    } catch (error) {
      logger.error("Failed to fetch customer:", error);
      throw error;
    }
  }

  static async updateCustomer(id: string, data: UpdateCustomerInput) {
    try {
      const existingCustomer = await prisma.customer.findUnique({
        where: { id },
      });

      if (!existingCustomer) {
        throw new CustomError("Customer not found", 404);
      }

      // Check for email conflicts if being updated
      if (data.email) {
        const conflictCustomer = await prisma.customer.findFirst({
          where: {
            AND: [{ id: { not: id } }, { email: data.email }],
          },
        });

        if (conflictCustomer) {
          throw new CustomError("Customer with this email already exists", 409);
        }
      }

      // Check for phone conflicts if being updated
      if (data.phone) {
        const conflictCustomer = await prisma.customer.findFirst({
          where: {
            AND: [{ id: { not: id } }, { phone: data.phone }],
          },
        });

        if (conflictCustomer) {
          throw new CustomError(
            "Customer with this phone number already exists",
            409
          );
        }
      }

      const customer = await prisma.customer.update({
        where: { id },
        data,
        include: {
          _count: {
            select: { orders: true },
          },
        },
      });

      logger.info(
        `Customer updated: ${customer.firstName} ${customer.lastName} (ID: ${customer.id})`
      );
      return customer;
    } catch (error) {
      logger.error("Customer update failed:", error);
      throw error;
    }
  }

  static async deleteCustomer(id: string) {
    try {
      const existingCustomer = await prisma.customer.findUnique({
        where: { id },
        include: {
          orders: true,
        },
      });

      if (!existingCustomer) {
        throw new CustomError("Customer not found", 404);
      }

      // Check if customer has orders
      if (existingCustomer.orders.length > 0) {
        throw new CustomError(
          "Cannot delete customer with existing orders. Consider deactivating instead.",
          400
        );
      }

      await prisma.customer.delete({
        where: { id },
      });

      logger.info(
        `Customer deleted: ${existingCustomer.firstName} ${existingCustomer.lastName} (ID: ${id})`
      );
      return { message: "Customer deleted successfully" };
    } catch (error) {
      logger.error("Customer deletion failed:", error);
      throw error;
    }
  }
}
