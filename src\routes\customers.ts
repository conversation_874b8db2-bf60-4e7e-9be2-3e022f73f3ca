import { UserRole } from "@prisma/client";
import { Router } from "express";
import { CustomerController } from "../controllers/customerController";
import { authenticate, authorize } from "../middleware/auth";
import { validate } from "../middleware/validation";
import {
  createCustomerSchema,
  customerParamsSchema,
  customerQuerySchema,
  updateCustomerSchema,
} from "../schemas/customer";

const router = Router();

// All customer routes require authentication
router.use(authenticate);

// Get all customers (All authenticated users)
router.get("/", validate(customerQuerySchema), CustomerController.getCustomers);

// Create customer (All authenticated users can create customers)
router.post(
  "/",
  validate(createCustomerSchema),
  CustomerController.createCustomer
);

// Get customer by ID (All authenticated users)
router.get(
  "/:id",
  validate(customerParamsSchema),
  CustomerController.getCustomerById
);

// Update customer (All authenticated users)
router.put(
  "/:id",
  validate(customerParamsSchema),
  validate(updateCustomerSchema),
  CustomerController.updateCustomer
);

// Delete customer (ADMIN and MANAGER only)
router.delete(
  "/:id",
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(customerParamsSchema),
  CustomerController.deleteCustomer
);

export default router;
