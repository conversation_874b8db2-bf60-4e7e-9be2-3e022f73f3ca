import { z } from 'zod';

export const createCustomerSchema = z.object({
  body: z.object({
    firstName: z.string().min(1, 'First name is required').optional(),
    lastName: z.string().min(1, 'Last name is required').optional(),
    email: z.string().email('Invalid email format').optional(),
    phone: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
    dateOfBirth: z.string().datetime('Invalid date format').transform((str) => new Date(str)).optional()
  }).refine(
    (data) => data.firstName || data.lastName || data.email || data.phone,
    {
      message: 'At least one of firstName, lastName, email, or phone is required',
      path: ['firstName']
    }
  )
});

export const updateCustomerSchema = z.object({
  body: z.object({
    firstName: z.string().min(1, 'First name is required').optional(),
    lastName: z.string().min(1, 'Last name is required').optional(),
    email: z.string().email('Invalid email format').optional(),
    phone: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
    dateOfBirth: z.string().datetime('Invalid date format').transform((str) => new Date(str)).optional(),
    isActive: z.boolean().optional()
  })
});

export const customerParamsSchema = z.object({
  params: z.object({
    id: z.string().cuid('Invalid customer ID format')
  })
});

export const customerQuerySchema = z.object({
  query: z.object({
    page: z.string().optional().default('1').transform(Number).pipe(z.number().min(1)),
    limit: z.string().optional().default('10').transform(Number).pipe(z.number().min(1).max(100)),
    search: z.string().optional(),
    isActive: z.string().transform((val) => val === 'true').optional()
  })
});

export type CreateCustomerInput = z.infer<typeof createCustomerSchema>['body'];
export type UpdateCustomerInput = z.infer<typeof updateCustomerSchema>['body'];
export type CustomerParams = z.infer<typeof customerParamsSchema>['params'];
export type CustomerQuery = z.infer<typeof customerQuerySchema>['query'];
