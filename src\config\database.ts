import { PrismaClient } from "@prisma/client";
import { initializeApp } from "firebase/app";
import { logger } from "./logger";

// Import the functions you need from the SDKs you need
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDsOI-c8szL-m_GkvuHEcJQWGcWQVEeo2E",
  authDomain: "cash-e4596.firebaseapp.com",
  databaseURL:
    "https://cash-e4596-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "cash-e4596",
  storageBucket: "cash-e4596.firebasestorage.app",
  messagingSenderId: "974664736594",
  appId: "1:974664736594:web:e7fdaa429d23a93a43a48b",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
// Create Prisma client instance
export const prisma = new PrismaClient({
  log: [
    {
      emit: "event",
      level: "query",
    },
    {
      emit: "event",
      level: "error",
    },
    {
      emit: "event",
      level: "info",
    },
    {
      emit: "event",
      level: "warn",
    },
  ],
});

// Log database queries in development
if (process.env.NODE_ENV === "development") {
  prisma.$on("query", (e) => {
    logger.debug("Query: " + e.query);
    logger.debug("Params: " + e.params);
    logger.debug("Duration: " + e.duration + "ms");
  });
}

// Log database errors
prisma.$on("error", (e) => {
  logger.error("Database error:", e);
});

// Log database info
prisma.$on("info", (e) => {
  logger.info("Database info:", e.message);
});

// Log database warnings
prisma.$on("warn", (e) => {
  logger.warn("Database warning:", e.message);
});

// Graceful shutdown
process.on("beforeExit", async () => {
  logger.info("Disconnecting from database...");
  await prisma.$disconnect();
});

export default prisma;
