import bcrypt from 'bcryptjs';
import { prisma } from '../config/database';
import { CustomError } from '../middleware/errorHandler';
import { CreateUserInput, UpdateUserInput, UserQuery } from '../schemas/user';
import { logger } from '../config/logger';

export class UserService {
  private static readonly SALT_ROUNDS = 12;

  static async createUser(data: CreateUserInput) {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: data.email },
            { username: data.username }
          ]
        }
      });

      if (existingUser) {
        throw new CustomError('User with this email or username already exists', 409);
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, this.SALT_ROUNDS);

      // Create user
      const user = await prisma.user.create({
        data: {
          ...data,
          password: hashedPassword
        },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        }
      });

      logger.info(`User created: ${user.email}`);
      return user;
    } catch (error) {
      logger.error('User creation failed:', error);
      throw error;
    }
  }

  static async getUsers(query: UserQuery) {
    try {
      const { page, limit, search, role, isActive } = query;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (role) {
        where.role = role;
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
            createdAt: true,
            updatedAt: true
          },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.user.count({ where })
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to fetch users:', error);
      throw error;
    }
  }

  static async getUserById(id: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        }
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      return user;
    } catch (error) {
      logger.error('Failed to fetch user:', error);
      throw error;
    }
  }

  static async updateUser(id: string, data: UpdateUserInput) {
    try {
      // Check if user exists
      const existingUser = await prisma.user.findUnique({
        where: { id }
      });

      if (!existingUser) {
        throw new CustomError('User not found', 404);
      }

      // Check for email/username conflicts
      if (data.email || data.username) {
        const conflictUser = await prisma.user.findFirst({
          where: {
            AND: [
              { id: { not: id } },
              {
                OR: [
                  ...(data.email ? [{ email: data.email }] : []),
                  ...(data.username ? [{ username: data.username }] : [])
                ]
              }
            ]
          }
        });

        if (conflictUser) {
          throw new CustomError('Email or username already exists', 409);
        }
      }

      const user = await prisma.user.update({
        where: { id },
        data,
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        }
      });

      logger.info(`User updated: ${user.email}`);
      return user;
    } catch (error) {
      logger.error('User update failed:', error);
      throw error;
    }
  }

  static async deleteUser(id: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id }
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      await prisma.user.delete({
        where: { id }
      });

      logger.info(`User deleted: ${user.email}`);
      return { message: 'User deleted successfully' };
    } catch (error) {
      logger.error('User deletion failed:', error);
      throw error;
    }
  }
}
