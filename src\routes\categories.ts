import { Router } from 'express';
import { CategoryController } from '../controllers/categoryController';
import { validate } from '../middleware/validation';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '@prisma/client';
import { 
  createCategorySchema, 
  updateCategorySchema, 
  categoryParamsSchema, 
  categoryQuerySchema 
} from '../schemas/category';

const router = Router();

// All category routes require authentication
router.use(authenticate);

// Get all categories (All authenticated users)
router.get('/', 
  validate(categoryQuerySchema),
  CategoryController.getCategories
);

// Create category (ADMIN and MANAGER only)
router.post('/', 
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(createCategorySchema),
  CategoryController.createCategory
);

// Get category by ID (All authenticated users)
router.get('/:id', 
  validate(categoryParamsSchema),
  CategoryController.getCategoryById
);

// Update category (ADMIN and MANAGER only)
router.put('/:id', 
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  validate(categoryParamsSchema),
  validate(updateCategorySchema),
  CategoryController.updateCategory
);

// Delete category (ADMIN only)
router.delete('/:id', 
  authorize(UserRole.ADMIN),
  validate(categoryParamsSchema),
  CategoryController.deleteCategory
);

export default router;
